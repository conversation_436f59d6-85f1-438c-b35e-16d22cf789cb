<!-- BACKUP OF ORIGINAL DYNAMIC-FORM COMPONENT HTML - DO NOT MODIFY -->
<!-- This is a complete backup of the original template before refactoring -->
<!-- Created on: 2025-01-02 -->

@if (showSuccessPopup) {
  <div class="popup">
    <div class="popup-content">
      <span class="close" (click)="closeSuccessPopup()">&times;</span>
      <p>Record Inserted Successfully</p>
    </div>
  </div>
}

@if (!submissionSuccess) {
  @if (showInitialInput) {
    <div class="initial-input">
      <form [formGroup]="form" class="form-container">
        <div class="form-main-field">
          <label for="ID" class="form-label">ID</label>
          <div class="input-button-group">
            <div class="id-input-container">
              <input formControlName="ID" id="ID" type="text" class="form-input" [class]="getInputClass()" placeholder="Enter ID" required 
                     (input)="onIdInputChange($event)" 
                     (focus)="onIdInputFocus()" 
                     (blur)="onIdInputBlur()" />
              
              <!-- Arrow button to toggle dropdown -->
              <button type="button" class="dropdown-arrow-btn" (click)="toggleIdDropdown()" matTooltip="Show ID suggestions">
                <mat-icon>{{ showIdDropdown ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
              </button>
              
              <!-- Dropdown list for filtered results -->
              @if (showIdDropdown) {
                <div class="id-dropdown">
                  @if (filteredIdOptions.length > 0) {
                    @for (option of filteredIdOptions; track option.ID) {
                      <div class="id-dropdown-item" (click)="selectIdOption(option)">
                        {{ option.ID }}
                      </div>
                    }
                  } @else {
                    <div class="id-dropdown-empty">
                      No IDs found
                    </div>
                  }
                </div>
              }
            </div>

            <!-- زر الإضافة -->
            <button mat-raised-button color="primary" type="button" 
                    (click)="loadDataAndBuildForm()" 
                    matTooltip="Add"
                    class="initial-input-button add-button">
              <mat-icon>add</mat-icon>
            </button>

            <!-- زر التعديل -->
            <button mat-raised-button color="primary" type="button" 
                    (click)="loadDataAndBuildForm()" 
                    matTooltip="Edit"
                    class="initial-input-button edit-button">
              <mat-icon>edit</mat-icon>
            </button>

            <!-- زر العرض -->
            <button mat-raised-button color="accent" type="button" 
                    (click)="viewData()" 
                    matTooltip="View"
                    class="initial-input-button view-button">
              <mat-icon>visibility</mat-icon>
            </button>

            <!-- زر الصيانة -->
            <button mat-raised-button color="warn" type="button" 
                    matTooltip="Maintenance"
                    class="initial-input-button maintenance-button">
              <mat-icon>build</mat-icon>
            </button>
          </div>
        </div>
      </form>
    </div>
  }

  @if (errorMessage) {
    <div class="error-message">{{ errorMessage }}</div>
  }

  @if (!showInitialInput) {
    <div class="form-grid">
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div class="horizontal-container">
      <div class="form-field">
        <!-- <label>ID</label> -->
        <p>{{ form.get('ID')?.value }}</p>
      </div>

      <div class="button-group">
          <!-- View toggle button -->
          <button mat-raised-button color="primary" type="button" (click)="toggleViewMode()" matTooltip="Toggle View"
                  class="form-action-button toggle-view-button">
            <mat-icon>{{ isRowView ? 'view_list' : 'view_module' }}</mat-icon>
            {{ isRowView ? 'Nested View' : 'Row View' }}
          </button>

          <!-- submit button -->
          @if (!isViewMode) {
            <button mat-raised-button color="primary" type="submit" [disabled]="isViewMode" matTooltip="Submit"
                    class="form-action-button submit-button">
              <mat-icon>send</mat-icon>
            </button>
          }

          <!-- Validate button -->
          @if (!isViewMode) {
            <button mat-raised-button color="accent" type="button" (click)="validateRecord()" matTooltip="Validate"
                    class="form-action-button validate-button">
              <mat-icon>check_circle</mat-icon>
            </button>
          }

          <!-- Authorize button -->
          @if (!isViewMode) {
            <button mat-raised-button color="accent" type="button" (click)="authorizeRecord()" matTooltip="Authorize"
                    class="form-action-button authorize-button">
              <mat-icon>verified</mat-icon>
            </button>
          }

          <!-- back button -->
          <button mat-raised-button color="primary" type="button" (click)="goBack()" matTooltip="Back"
                  class="form-action-button back-button">
            <mat-icon>arrow_back</mat-icon>
          </button>

          <!-- Reject button -->
          @if (!isViewMode) {
            <button mat-raised-button color="warn" type="button" matTooltip="Reject"
                    class="form-action-button reject-button">
              <mat-icon>cancel</mat-icon>
            </button>
          }

          <!-- Delete button -->
          @if (!isViewMode) {
            <button mat-raised-button color="warn" type="button" matTooltip="Delete"
                    class="form-action-button delete-button">
              <mat-icon>delete</mat-icon>
            </button>
          }
        @if (errorMessage) {
          <div class="error-message">{{ errorMessage }}</div>
        }
      </div>


    </div>
      <!-- 🔹 All Fields in Order (Unified Rendering) -->
      <div class="form-grid" [ngClass]="'columns-' + columnCount">
        <div class="form-column" *ngFor="let column of columns">
          <ng-container *ngFor="let field of column">
          <!-- Skip ID field as it's handled separately -->
          @if (field.fieldName?.toUpperCase() !== 'ID') {

            <!-- 🔸 Non-Grouped Fields -->
            @if (!field.Group) {
              <div class="form-field">

                <!-- 🔸 Regular Field -->
                @if (!field.isMulti) {
                  <label [for]="field.fieldName">{{ field.label || field.fieldName }} @if (field.mandatory) {<span>*</span>}
                @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
               </label>

                  @if (field.foreginKey) {
                    <!-- Check if this is a type field (fieldType) -->
                    @if (field.foreginKey === 'fieldType') {
                      <div class="dropdown-input-container">
                        <input [formControlName]="field.fieldName" [id]="field.fieldName" type="text" 
                               class="form-input dropdown-input" 
                               (input)="onTypeInputChange($event, field.fieldName)" 
                               (focus)="onTypeInputFocus(field.fieldName)" 
                               (blur)="onTypeInputBlur(field.fieldName)"
                               [disabled]="isViewMode || field.noInput"
                               [placeholder]="'Search ' + field.label?.trim() || field.fieldName" />
                        
                        <!-- Arrow button to toggle dropdown -->
                        <button type="button" class="dropdown-arrow-btn" 
                                (click)="toggleTypeDropdown(field.fieldName)" 
                                [disabled]="isViewMode || field.noInput"
                                matTooltip="Show type suggestions">
                          <mat-icon>{{ showTypeDropdown[field.fieldName] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                        </button>
                        
                        <!-- Dropdown list for filtered results -->
                        @if (showTypeDropdown[field.fieldName]) {
                          <div class="dropdown-list">
                            @if (filteredTypeOptions[field.fieldName] && filteredTypeOptions[field.fieldName].length > 0) {
                              @for (option of filteredTypeOptions[field.fieldName]; track option.ROW_ID) {
                                <div class="dropdown-item" (click)="selectTypeOption(option, field.fieldName)">
                                  {{ option.ROW_ID }}
                                </div>
                              }
                            } @else {
                              <div class="dropdown-empty">
                                No types found
                              </div>
                            }
                          </div>
                        }
                      </div>
                    }
                    <!-- Check if this is a foreign key field (formDefinition) -->
                    @else if (field.foreginKey === 'formDefinition') {
                      <div class="dropdown-input-container">
                        <input [formControlName]="field.fieldName" [id]="field.fieldName" type="text" 
                               class="form-input dropdown-input" 
                               (input)="onForeignKeyInputChange($event, field.fieldName)" 
                               (focus)="onForeignKeyInputFocus(field.fieldName)" 
                               (blur)="onForeignKeyInputBlur(field.fieldName)"
                               [disabled]="isViewMode || field.noInput"
                              [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
                        
                              
                        <!-- Arrow button to toggle dropdown -->
                        <button type="button" class="dropdown-arrow-btn" 
                                (click)="toggleForeignKeyDropdown(field.fieldName)" 
                                [disabled]="isViewMode || field.noInput"
                                matTooltip="Show foreign key suggestions">
                          <mat-icon>{{ showForeignKeyDropdown[field.fieldName] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                        </button>
                        
                        <!-- Dropdown list for filtered results -->
                        @if (showForeignKeyDropdown[field.fieldName]) {
                          <div class="dropdown-list">
                            @if (filteredForeignKeyOptions[field.fieldName] && filteredForeignKeyOptions[field.fieldName].length > 0) {
                              @for (option of filteredForeignKeyOptions[field.fieldName]; track option.ROW_ID) {
                                <div class="dropdown-item" (click)="selectForeignKeyOption(option, field.fieldName)">
                                  {{ option.ROW_ID }}
                                </div>
                              }
                            } @else {
                              <div class="dropdown-empty">
                                No foreign keys found
                              </div>
                            }
                          </div>
                        }
                      </div>
                    }
                    <!-- Default dropdown for other foreign keys -->
                    @else {
                      <div class="dropdown-input-container">
                        <input [formControlName]="field.fieldName" [id]="field.fieldName" type="text" 
                               class="form-input dropdown-input" 
                               (input)="onRegularInputChange($event, field.fieldName)" 
                               (focus)="onRegularInputFocus(field.fieldName)" 
                               (blur)="onRegularInputBlur(field.fieldName)"
                               [disabled]="isViewMode || field.noInput"
                               [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
                        
                        <!-- Arrow button to toggle dropdown -->
                        <button type="button" class="dropdown-arrow-btn" 
                                (click)="toggleRegularDropdown(field.fieldName)" 
                                [disabled]="isViewMode || field.noInput"
                                matTooltip="Show options">
                          <mat-icon>{{ showRegularDropdown[field.fieldName] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                        </button>
                        
                        <!-- Dropdown list for filtered results -->
                        @if (showRegularDropdown[field.fieldName]) {
                          <div class="dropdown-list">
                            @if (filteredRegularOptions[field.fieldName] && filteredRegularOptions[field.fieldName].length > 0) {
                              @for (option of filteredRegularOptions[field.fieldName]; track option.ROW_ID) {
                                <div class="dropdown-item" (click)="selectRegularOption(option, field.fieldName)">
                                  @for (key of getKeys(option); track key) {
                                    {{ option[key] }}&nbsp;
                                  }
                                </div>
                              }
                            } @else {
                              <div class="dropdown-empty">
                                No options found
                              </div>
                            }
                          </div>
                        }
                      </div>
                    }
                  } @else {
                    <!-- Regular input fields for non-foreign key fields -->
                    @if (field.type === 'boolean') {
                      <input [formControlName]="field.fieldName" [id]="field.fieldName"
                        type="checkbox" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
                    }
                    @if (field.type === 'string') {
                      <input [formControlName]="field.fieldName" [id]="field.fieldName"
                        type="text" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" [placeholder]="(field.label?.trim() || field.fieldName)" />
                    }
                    @if (field.type === 'int') {
                      <input [formControlName]="field.fieldName" [id]="field.fieldName"
                        type="number" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
                    }
                    @if (field.type === 'date') {
                      <input [formControlName]="field.fieldName" [id]="field.fieldName"
                        type="date" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
                    }
                    @if (field.type === 'double') {
                      <input [formControlName]="field.fieldName" [id]="field.fieldName"
                        type="number" step="00.50" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
                    }
                  }
                }
