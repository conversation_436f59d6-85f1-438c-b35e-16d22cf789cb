<div class="horizontal-container">
  <div class="form-field">
    <p>{{ formId }}</p>
  </div>

  <div class="button-group">
    <!-- View toggle button -->
    <button mat-raised-button color="primary" type="button" (click)="onToggleViewMode()" matTooltip="Toggle View"
            class="form-action-button toggle-view-button">
      <mat-icon>{{ isRowView ? 'view_list' : 'view_module' }}</mat-icon>
      {{ isRowView ? 'Nested View' : 'Row View' }}
    </button>
  </div>

  @if (errorMessage) {
    <div class="error-message">{{ errorMessage }}</div>
  }
</div>
