.horizontal-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-field p {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
  padding: 8px 12px;
  background-color: #e9ecef;
  border-radius: 4px;
  border: 1px solid #ced4da;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  align-items: center;
}

.form-action-button {
  min-width: 100px;
  height: 40px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.toggle-view-button {
  background-color: #6f42c1;
  color: white;
}

.toggle-view-button:hover {
  background-color: #5a32a3;
}

.error-message {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  padding: 12px 16px;
  margin: 16px 0;
  text-align: center;
  font-weight: 500;
}
