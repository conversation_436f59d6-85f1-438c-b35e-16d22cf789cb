import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormHeaderComponent } from './form-header.component';

describe('FormHeaderComponent', () => {
  let component: FormHeaderComponent;
  let fixture: ComponentFixture<FormHeaderComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FormHeaderComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(FormHeaderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit toggleViewMode when button is clicked', () => {
    spyOn(component.toggleViewMode, 'emit');
    component.onToggleViewMode();
    expect(component.toggleViewMode.emit).toHaveBeenCalled();
  });

  it('should display form ID', () => {
    component.formId = 'TEST123';
    fixture.detectChanges();
    const formIdElement = fixture.nativeElement.querySelector('.form-field p');
    expect(formIdElement.textContent.trim()).toBe('TEST123');
  });

  it('should show error message when provided', () => {
    component.errorMessage = 'Test error';
    fixture.detectChanges();
    const errorElement = fixture.nativeElement.querySelector('.error-message');
    expect(errorElement.textContent.trim()).toBe('Test error');
  });
});
