import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SuccessPopupComponent } from './success-popup.component';

describe('SuccessPopupComponent', () => {
  let component: SuccessPopupComponent;
  let fixture: ComponentFixture<SuccessPopupComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SuccessPopupComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(SuccessPopupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit closed event when closePopup is called', () => {
    spyOn(component.closed, 'emit');
    component.closePopup();
    expect(component.closed.emit).toHaveBeenCalled();
  });

  it('should not show popup when show is false', () => {
    component.show = false;
    fixture.detectChanges();
    const popup = fixture.nativeElement.querySelector('.popup');
    expect(popup).toBeFalsy();
  });

  it('should show popup when show is true', () => {
    component.show = true;
    fixture.detectChanges();
    const popup = fixture.nativeElement.querySelector('.popup');
    expect(popup).toBeTruthy();
  });
});
