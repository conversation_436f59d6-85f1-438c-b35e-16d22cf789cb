import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormControl } from '@angular/forms';
import { IdInputComponent, IdActionType } from './id-input.component';

describe('IdInputComponent', () => {
  let component: IdInputComponent;
  let fixture: ComponentFixture<IdInputComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [IdInputComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(IdInputComponent);
    component = fixture.componentInstance;
    component.formControl = new FormControl('');
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit actionClicked when onActionClick is called', () => {
    spyOn(component.actionClicked, 'emit');
    const action: IdActionType = 'add';
    component.onActionClick(action);
    expect(component.actionClicked.emit).toHaveBeenCalledWith(action);
  });

  it('should emit idSelected when selectIdOption is called', () => {
    spyOn(component.idSelected, 'emit');
    const option = { ID: 'TEST123' };
    component.selectIdOption(option);
    expect(component.idSelected.emit).toHaveBeenCalledWith('TEST123');
  });

  it('should return invalid class when validation is shown and value is empty', () => {
    component.showValidation = true;
    component.formControl.setValue('');
    expect(component.getInputClass()).toBe('invalid');
  });

  it('should return empty class when validation is shown and value is not empty', () => {
    component.showValidation = true;
    component.formControl.setValue('TEST123');
    expect(component.getInputClass()).toBe('');
  });

  it('should toggle dropdown visibility', () => {
    expect(component.showIdDropdown).toBeFalsy();
    component.toggleIdDropdown();
    expect(component.showIdDropdown).toBeTruthy();
    component.toggleIdDropdown();
    expect(component.showIdDropdown).toBeFalsy();
  });
});
