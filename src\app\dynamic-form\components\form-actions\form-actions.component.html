<div class="button-group">
  <!-- Submit button -->
  @if (!isViewMode) {
    <button mat-raised-button color="primary" type="button" 
            [disabled]="isViewMode || isLoading || !formValid" 
            (click)="onActionClick('submit')"
            matTooltip="Submit"
            class="form-action-button submit-button">
      <mat-icon>send</mat-icon>
    </button>
  }

  <!-- Validate button -->
  @if (!isViewMode) {
    <button mat-raised-button color="accent" type="button" 
            [disabled]="isLoading"
            (click)="onActionClick('validate')" 
            matTooltip="Validate"
            class="form-action-button validate-button">
      <mat-icon>check_circle</mat-icon>
    </button>
  }

  <!-- Authorize button -->
  @if (!isViewMode) {
    <button mat-raised-button color="accent" type="button" 
            [disabled]="isLoading"
            (click)="onActionClick('authorize')" 
            matTooltip="Authorize"
            class="form-action-button authorize-button">
      <mat-icon>verified</mat-icon>
    </button>
  }

  <!-- Back button -->
  <button mat-raised-button color="primary" type="button" 
          [disabled]="isLoading"
          (click)="onActionClick('back')" 
          matTooltip="Back"
          class="form-action-button back-button">
    <mat-icon>arrow_back</mat-icon>
  </button>

  <!-- Reject button -->
  @if (!isViewMode) {
    <button mat-raised-button color="warn" type="button" 
            [disabled]="isLoading"
            (click)="onActionClick('reject')"
            matTooltip="Reject"
            class="form-action-button reject-button">
      <mat-icon>cancel</mat-icon>
    </button>
  }

  <!-- Delete button -->
  @if (!isViewMode) {
    <button mat-raised-button color="warn" type="button" 
            [disabled]="isLoading"
            (click)="onActionClick('delete')"
            matTooltip="Delete"
            class="form-action-button delete-button">
      <mat-icon>delete</mat-icon>
    </button>
  }
</div>
