import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

export type FormActionType = 'submit' | 'validate' | 'authorize' | 'back' | 'reject' | 'delete';

@Component({
  selector: 'app-form-actions',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatIconModule, MatTooltipModule],
  templateUrl: './form-actions.component.html',
  styleUrl: './form-actions.component.scss'
})
export class FormActionsComponent {
  @Input() isViewMode: boolean = false;
  @Input() isLoading: boolean = false;
  @Input() formValid: boolean = true;
  @Output() actionClicked = new EventEmitter<FormActionType>();

  onActionClick(action: FormActionType) {
    this.actionClicked.emit(action);
  }
}
