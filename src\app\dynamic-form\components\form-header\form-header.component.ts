import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-form-header',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatIconModule, MatTooltipModule],
  templateUrl: './form-header.component.html',
  styleUrl: './form-header.component.scss'
})
export class FormHeaderComponent {
  @Input() formId: string = '';
  @Input() isViewMode: boolean = false;
  @Input() isRowView: boolean = false;
  @Input() errorMessage: string = '';
  @Output() toggleViewMode = new EventEmitter<void>();

  onToggleViewMode() {
    this.toggleViewMode.emit();
  }
}
