import { Component, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

export type IdActionType = 'add' | 'edit' | 'view' | 'maintenance';

@Component({
  selector: 'app-id-input',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, MatButtonModule, MatIconModule, MatTooltipModule],
  templateUrl: './id-input.component.html',
  styleUrl: './id-input.component.scss'
})
export class IdInputComponent implements OnDestroy {
  @Input() formControl!: FormControl;
  @Input() tableName: string = '';
  @Input() screenName: string = '';
  @Input() showValidation: boolean = false;
  @Output() idSelected = new EventEmitter<string>();
  @Output() actionClicked = new EventEmitter<IdActionType>();
  @Output() validationChange = new EventEmitter<boolean>();

  showIdDropdown = false;
  idOptions: any[] = [];
  filteredIdOptions: any[] = [];
  idSearchTimeout: any;

  ngOnDestroy() {
    if (this.idSearchTimeout) {
      clearTimeout(this.idSearchTimeout);
    }
  }

  getInputClass(): string {
    return this.showValidation && (!this.formControl.value || this.formControl.value.trim() === '') ? 'invalid' : '';
  }

  onIdInputChange(event: any) {
    const value = event.target.value;
    this.formControl.setValue(value);
    
    // Clear previous timeout
    if (this.idSearchTimeout) {
      clearTimeout(this.idSearchTimeout);
    }

    // Set new timeout for search
    this.idSearchTimeout = setTimeout(() => {
      this.filterIdOptions(value);
    }, 300);

    // Emit validation change
    this.validationChange.emit(value && value.trim() !== '');
  }

  onIdInputFocus() {
    this.loadIdOptions();
    this.showIdDropdown = true;
  }

  onIdInputBlur() {
    // Delay hiding dropdown to allow for click events
    setTimeout(() => {
      this.showIdDropdown = false;
    }, 200);
  }

  toggleIdDropdown() {
    if (this.showIdDropdown) {
      this.showIdDropdown = false;
    } else {
      this.loadIdOptions();
      this.showIdDropdown = true;
    }
  }

  selectIdOption(option: any) {
    this.formControl.setValue(option.ID);
    this.showIdDropdown = false;
    this.idSelected.emit(option.ID);
    this.validationChange.emit(true);
  }

  onActionClick(action: IdActionType) {
    this.actionClicked.emit(action);
  }

  private loadIdOptions() {
    // TODO: Implement API call to load ID options
    // This will be implemented when integrating with the main component
    this.idOptions = [];
    this.filteredIdOptions = [];
  }

  private filterIdOptions(searchTerm: string) {
    if (!searchTerm || searchTerm.trim() === '') {
      this.filteredIdOptions = this.idOptions;
    } else {
      this.filteredIdOptions = this.idOptions.filter(option =>
        option.ID && option.ID.toString().toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
  }
}
