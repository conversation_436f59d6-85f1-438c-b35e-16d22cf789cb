import { Component, Input, Output, EventEmitter, OnDestroy, inject } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { environment } from '../../../environments/environment';

export type IdActionType = 'add' | 'edit' | 'view' | 'maintenance';

@Component({
  selector: 'app-id-input',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, MatButtonModule, MatIconModule, MatTooltipModule, HttpClientModule],
  templateUrl: './id-input.component.html',
  styleUrl: './id-input.component.scss'
})
export class IdInputComponent implements On<PERSON><PERSON>roy {
  @Input() formControl!: FormControl;
  @Input() tableName: string = '';
  @Input() screenName: string = '';
  @Input() showValidation: boolean = false;
  @Output() idSelected = new EventEmitter<string>();
  @Output() actionClicked = new EventEmitter<IdActionType>();
  @Output() validationChange = new EventEmitter<boolean>();

  showIdDropdown = false;
  idOptions: any[] = [];
  filteredIdOptions: any[] = [];
  idSearchTimeout: any;

  private http = inject(HttpClient);

  ngOnDestroy() {
    if (this.idSearchTimeout) {
      clearTimeout(this.idSearchTimeout);
    }
  }

  getInputClass(): string {
    return this.showValidation ? 'invalid-input' : '';
  }

  onIdInputChange(event: any) {
    const value = event.target.value;
    this.formControl.setValue(value);

    if (value && value.trim() !== '') {
      this.validationChange.emit(true);
    }

    // Clear previous timeout
    if (this.idSearchTimeout) {
      clearTimeout(this.idSearchTimeout);
    }

    // Set new timeout for search
    this.idSearchTimeout = setTimeout(() => {
      this.searchIdOptions(value);
    }, 300);
  }

  onIdInputFocus() {
    const currentValue = this.formControl.value || '';
    if (currentValue.trim() === '') {
      this.loadAllIds();
    } else {
      this.searchIdOptions(currentValue);
    }
  }

  onIdInputBlur() {
    // Delay hiding dropdown to allow for click events
    setTimeout(() => {
      this.showIdDropdown = false;
    }, 200);
  }

  toggleIdDropdown() {
    if (!this.showIdDropdown) {
      const currentValue = this.formControl.value || '';
      // If no value is entered, show all IDs
      if (currentValue.trim() === '') {
        this.loadAllIds();
      } else {
        this.searchIdOptions(currentValue);
      }
    } else {
      this.showIdDropdown = false;
    }
  }

  selectIdOption(option: any) {
    this.formControl.setValue(option.ID);
    this.showIdDropdown = false;
    this.idSelected.emit(option.ID);
    // Reset validation state when a valid ID is selected
    this.validationChange.emit(true);
  }

  onActionClick(action: IdActionType) {
    this.actionClicked.emit(action);
  }

  // Helper method to extract part before comma for query-builder API calls
  private extractQueryBuilderId(): string {
    const nameToUse = this.screenName || this.tableName;
    if (nameToUse && nameToUse.includes(',')) {
      return nameToUse.split(',')[0].trim();
    }
    return nameToUse;
  }

  searchIdOptions(searchTerm: string): void {
    if (!this.tableName && !this.screenName) {
      return;
    }

    const queryBuilderId = this.extractQueryBuilderId();
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = {
      ID: {
        CT: searchTerm // CT = Contains operator
      },
      _select: ["ID"],
      _limit: 20
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.filteredIdOptions = response;
          this.showIdDropdown = true;
        } else {
          this.filteredIdOptions = [];
          this.showIdDropdown = true; // Show dropdown with "No IDs found" message
        }
      },
      error: (error) => {
        this.filteredIdOptions = [];
        this.showIdDropdown = true; // Show dropdown with "No IDs found" message
      }
    });
  }

  loadAllIds(): void {
    if (!this.tableName && !this.screenName) {
      return;
    }

    // Use the query-builder API to get all IDs
    const queryBuilderId = this.extractQueryBuilderId();
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = {
      _select: ["ID"],
      _limit: 100 // Higher limit to get more IDs
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.filteredIdOptions = response;
          this.showIdDropdown = true;
        } else {
          this.filteredIdOptions = [];
          this.showIdDropdown = true; // Show dropdown even if no data to show "No IDs found"
        }
      },
      error: (error) => {
        this.filteredIdOptions = [];
        this.showIdDropdown = true;
      }
    });
  }
}
