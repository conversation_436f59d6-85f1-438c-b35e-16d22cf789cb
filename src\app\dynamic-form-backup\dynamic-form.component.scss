/* BACKUP OF ORIGINAL DYNAMIC-FORM COMPONENT SCSS - DO NOT MODIFY */
/* This is a complete backup of the original styles before refactoring */
/* Created on: 2025-01-02 */

.form-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.initial-input {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
}

.form-main-field {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  width: 100%;
  max-width: 600px;
}

.form-label {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  text-align: center;
}

.input-button-group {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  justify-content: center;
  flex-wrap: wrap;
}

.id-input-container {
  position: relative;
  flex: 1;
  min-width: 200px;
  max-width: 300px;
}

.form-input {
  width: 100%;
  padding: 12px 40px 12px 16px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-input.invalid {
  border-color: #dc3545;
}

.dropdown-arrow-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: background-color 0.2s ease;
}

.dropdown-arrow-btn:hover {
  background-color: #f0f0f0;
  color: #333;
}

.dropdown-arrow-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.id-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 2px;
}

.id-dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.id-dropdown-item:hover {
  background-color: #f8f9fa;
}

.id-dropdown-item:last-child {
  border-bottom: none;
}

.id-dropdown-empty {
  padding: 12px 16px;
  color: #666;
  font-style: italic;
  text-align: center;
}

.initial-input-button {
  min-width: 120px;
  height: 48px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.initial-input-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.add-button {
  background-color: #28a745;
  color: white;
}

.add-button:hover {
  background-color: #218838;
}

.edit-button {
  background-color: #007bff;
  color: white;
}

.edit-button:hover {
  background-color: #0056b3;
}

.view-button {
  background-color: #17a2b8;
  color: white;
}

.view-button:hover {
  background-color: #138496;
}

.maintenance-button {
  background-color: #ffc107;
  color: #212529;
}

.maintenance-button:hover {
  background-color: #e0a800;
}

.error-message {
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  padding: 12px 16px;
  margin: 16px 0;
  text-align: center;
  font-weight: 500;
}

.form-grid {
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

.horizontal-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-field p {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
  padding: 8px 12px;
  background-color: #e9ecef;
  border-radius: 4px;
  border: 1px solid #ced4da;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  align-items: center;
}

.form-action-button {
  min-width: 100px;
  height: 40px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.toggle-view-button {
  background-color: #6f42c1;
  color: white;
}

.toggle-view-button:hover {
  background-color: #5a32a3;
}

.submit-button {
  background-color: #28a745;
  color: white;
}

.submit-button:hover {
  background-color: #218838;
}

.validate-button {
  background-color: #17a2b8;
  color: white;
}

.validate-button:hover {
  background-color: #138496;
}

.authorize-button {
  background-color: #fd7e14;
  color: white;
}

.authorize-button:hover {
  background-color: #e8690b;
}

.back-button {
  background-color: #6c757d;
  color: white;
}

.back-button:hover {
  background-color: #5a6268;
}

.reject-button {
  background-color: #dc3545;
  color: white;
}

.reject-button:hover {
  background-color: #c82333;
}

.delete-button {
  background-color: #dc3545;
  color: white;
}

.delete-button:hover {
  background-color: #c82333;
}

/* Grid Layout Styles */
.form-grid.columns-1 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.form-grid.columns-2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-grid.columns-3 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
}

.form-grid.columns-4 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 20px;
}

.form-column {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Form Field Styles */
.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.form-field label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
  margin-bottom: 4px;
}

.form-field label span {
  color: #dc3545;
  margin-left: 2px;
}

.no-input-indicator {
  color: #6c757d;
  font-weight: normal;
  font-size: 12px;
}

.form-field input,
.form-field select,
.form-field textarea {
  padding: 10px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  background-color: white;
}

.form-field input:focus,
.form-field select:focus,
.form-field textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

.form-field input:disabled,
.form-field select:disabled,
.form-field textarea:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.form-field input[readonly],
.form-field select[readonly],
.form-field textarea[readonly] {
  background-color: #f8f9fa;
  color: #495057;
}

/* Dropdown Styles */
.dropdown-input-container {
  position: relative;
  width: 100%;
}

.dropdown-input {
  width: 100%;
  padding-right: 40px !important;
  box-sizing: border-box;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ced4da;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 2px;
}

.dropdown-item {
  padding: 10px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
  font-size: 14px;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-empty {
  padding: 10px 12px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
  font-size: 14px;
}
