// BACKUP OF ORIGINAL TEST-NESTED-GROUPS - DO NOT MODIFY
// This is a complete backup of the original test data before refactoring
// Created on: 2025-01-02

export const testNestedGroupsData = {
  "success": true,
  "message": "Form definition retrieved successfully",
  "data": {
    "tableName": "testTable",
    "fieldName": [
      {
        "fieldName": "ID",
        "label": "ID",
        "type": "string",
        "mandatory": true,
        "Group": null,
        "isMulti": false,
        "foreginKey": null,
        "noInput": false
      },
      {
        "fieldName": "name",
        "label": "Name",
        "type": "string",
        "mandatory": true,
        "Group": "fieldName",
        "isMulti": false,
        "foreginKey": null,
        "noInput": false
      },
      {
        "fieldName": "description",
        "label": "Description", 
        "type": "string",
        "mandatory": false,
        "Group": "fieldName",
        "isMulti": false,
        "foreginKey": null,
        "noInput": false
      },
      {
        "fieldName": "typeField",
        "label": "Type Field",
        "type": "string",
        "mandatory": true,
        "Group": "type",
        "isMulti": false,
        "foreginKey": "fieldType",
        "noInput": false
      },
      {
        "fieldName": "typeDescription",
        "label": "Type Description",
        "type": "string", 
        "mandatory": false,
        "Group": "type",
        "isMulti": false,
        "foreginKey": null,
        "noInput": false
      },
      {
        "fieldName": "nestedField1",
        "label": "Nested Field 1",
        "type": "string",
        "mandatory": true,
        "Group": "type|field",
        "isMulti": false,
        "foreginKey": null,
        "noInput": false
      },
      {
        "fieldName": "nestedField2", 
        "label": "Nested Field 2",
        "type": "string",
        "mandatory": false,
        "Group": "type|field",
        "isMulti": false,
        "foreginKey": null,
        "noInput": false
      },
      {
        "fieldName": "nestedMultiField",
        "label": "Nested Multi Field",
        "type": "string",
        "mandatory": false,
        "Group": "type|field",
        "isMulti": true,
        "foreginKey": null,
        "noInput": false
      },
      {
        "fieldName": "anotherNestedField",
        "label": "Another Nested Field",
        "type": "string",
        "mandatory": false,
        "Group": "type|another",
        "isMulti": false,
        "foreginKey": null,
        "noInput": false
      },
      {
        "fieldName": "regularField",
        "label": "Regular Field",
        "type": "string",
        "mandatory": false,
        "Group": null,
        "isMulti": false,
        "foreginKey": null,
        "noInput": false
      },
      {
        "fieldName": "multiField",
        "label": "Multi Field",
        "type": "string",
        "mandatory": false,
        "Group": null,
        "isMulti": true,
        "foreginKey": null,
        "noInput": false
      }
    ],
    "columnNumber": 2,
    "isTenantBased": false,
    "defaultFields": [
      {
        "fieldName": "name",
        "defaultValue": "Default Name"
      },
      {
        "fieldName": "description", 
        "defaultValue": "Default Description"
      }
    ]
  }
};
