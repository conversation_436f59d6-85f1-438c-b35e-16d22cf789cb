import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-success-popup',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatIconModule],
  templateUrl: './success-popup.component.html',
  styleUrl: './success-popup.component.scss'
})
export class SuccessPopupComponent {
  @Input() show: boolean = false;
  @Input() message: string = 'Record Inserted Successfully';
  @Input() type: 'success' | 'error' | 'info' = 'success';
  @Output() closed = new EventEmitter<void>();

  closePopup() {
    this.closed.emit();
  }
}
