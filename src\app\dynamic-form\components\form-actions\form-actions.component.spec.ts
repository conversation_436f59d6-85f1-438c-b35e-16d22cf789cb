import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormActionsComponent, FormActionType } from './form-actions.component';

describe('FormActionsComponent', () => {
  let component: FormActionsComponent;
  let fixture: ComponentFixture<FormActionsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FormActionsComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(FormActionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit actionClicked when onActionClick is called', () => {
    spyOn(component.actionClicked, 'emit');
    const action: FormActionType = 'submit';
    component.onActionClick(action);
    expect(component.actionClicked.emit).toHaveBeenCalledWith(action);
  });

  it('should hide action buttons in view mode', () => {
    component.isViewMode = true;
    fixture.detectChanges();
    const submitButton = fixture.nativeElement.querySelector('.submit-button');
    const validateButton = fixture.nativeElement.querySelector('.validate-button');
    expect(submitButton).toBeFalsy();
    expect(validateButton).toBeFalsy();
  });

  it('should show back button in all modes', () => {
    component.isViewMode = true;
    fixture.detectChanges();
    const backButton = fixture.nativeElement.querySelector('.back-button');
    expect(backButton).toBeTruthy();
  });

  it('should disable submit button when form is invalid', () => {
    component.isViewMode = false;
    component.formValid = false;
    fixture.detectChanges();
    const submitButton = fixture.nativeElement.querySelector('.submit-button');
    expect(submitButton.disabled).toBeTruthy();
  });
});
